{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAA2B;AAC3B,yCAA2B;AAE3B,oDAA4B;AAC5B,kDAAgC;AAChC,2CAAqD;AACrD,6BAA0B;AAC1B,iEAA4D;AAG5D,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,mBAAmB,CAAC,CAAC;AAE/C,MAAM,0BAA0B,GAAG,CAGlC,OAAU,EACT,EAAE;IACH,IACC,OAAO,CAAC,UAAU,KAAK,SAAS;QAChC,OAAO,CAAC,IAAI;QACZ,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EACtB;QACD,OAAO;YACN,GAAG,OAAO;YACV,UAAU,EAAE,OAAO,CAAC,IAAI;SACxB,CAAC;KACF;IACD,OAAO,OAAO,CAAC;AAChB,CAAC,CAAC;AAqBF;;;;;;;;;;;GAWG;AACH,MAAa,eAAoC,SAAQ,kBAAK;IAO7D,YAAY,KAAgB,EAAE,IAAkC;QAC/D,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,OAAO,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QACnC,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,SAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAChE,IAAI,CAAC,YAAY,GAAG,IAAI,EAAE,OAAO,IAAI,EAAE,CAAC;QACxC,KAAK,CAAC,2CAA2C,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEpE,4CAA4C;QAC5C,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAC5D,UAAU,EACV,EAAE,CACF,CAAC;QACF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI;YAC3B,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;YAC/B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ;gBAClC,CAAC,CAAC,GAAG;gBACL,CAAC,CAAC,EAAE,CAAC;QACN,IAAI,CAAC,WAAW,GAAG;YAClB,sEAAsE;YACtE,aAAa,EAAE,CAAC,UAAU,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACxC,IAAI;YACJ,IAAI;SACJ,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO,CACZ,GAAuB,EACvB,IAAsB;QAEtB,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACf,MAAM,IAAI,SAAS,CAAC,oBAAoB,CAAC,CAAC;SAC1C;QAED,kDAAkD;QAClD,IAAI,MAAkB,CAAC;QACvB,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAChC,KAAK,CAAC,2BAA2B,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;SACnE;aAAM;YACN,KAAK,CAAC,2BAA2B,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACvC;QAED,MAAM,OAAO,GACZ,OAAO,IAAI,CAAC,YAAY,KAAK,UAAU;YACtC,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE;YACrB,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAC7B,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAClE,IAAI,OAAO,GAAG,WAAW,IAAI,IAAI,IAAI,CAAC,IAAI,eAAe,CAAC;QAE1D,wDAAwD;QACxD,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE;YACrC,MAAM,IAAI,GAAG,GAAG,kBAAkB,CACjC,KAAK,CAAC,QAAQ,CACd,IAAI,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,qBAAqB,CAAC,GAAG,SAAS,MAAM,CAAC,IAAI,CACpD,IAAI,CACJ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;SACvB;QAED,OAAO,CAAC,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QAEtC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;YACjC,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,SAAS;gBAC3C,CAAC,CAAC,YAAY;gBACd,CAAC,CAAC,OAAO,CAAC;SACX;QACD,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACxC,OAAO,IAAI,GAAG,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;SAC3C;QAED,MAAM,oBAAoB,GAAG,IAAA,yCAAkB,EAAC,MAAM,CAAC,CAAC;QAExD,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC;QAE/B,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,oBAAoB,CAAC;QACzD,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QAExC,IAAI,OAAO,CAAC,UAAU,KAAK,GAAG,EAAE;YAC/B,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE3B,IAAI,IAAI,CAAC,cAAc,EAAE;gBACxB,sDAAsD;gBACtD,8CAA8C;gBAC9C,KAAK,CAAC,oCAAoC,CAAC,CAAC;gBAC5C,OAAO,GAAG,CAAC,OAAO,CAAC;oBAClB,GAAG,IAAI,CACN,0BAA0B,CAAC,IAAI,CAAC,EAChC,MAAM,EACN,MAAM,EACN,MAAM,CACN;oBACD,MAAM;iBACN,CAAC,CAAC;aACH;YAED,OAAO,MAAM,CAAC;SACd;QAED,oEAAoE;QACpE,kEAAkE;QAClE,iEAAiE;QACjE,qBAAqB;QAErB,iEAAiE;QACjE,0DAA0D;QAC1D,oEAAoE;QACpE,mBAAmB;QACnB,EAAE;QACF,4CAA4C;QAC5C,MAAM,CAAC,OAAO,EAAE,CAAC;QAEjB,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;QACvD,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QAE3B,oEAAoE;QACpE,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAa,EAAE,EAAE;YACpC,KAAK,CAAC,2CAA2C,CAAC,CAAC;YACnD,IAAA,gBAAM,EAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YAEpC,gEAAgE;YAChE,8DAA8D;YAC9D,YAAY;YACZ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACnB,CAAC;;AA9IM,yBAAS,GAAG,CAAC,MAAM,EAAE,OAAO,CAAU,CAAC;AADlC,0CAAe;AAkJ5B,SAAS,MAAM,CAAC,MAAkC;IACjD,MAAM,CAAC,MAAM,EAAE,CAAC;AACjB,CAAC;AAED,SAAS,IAAI,CACZ,GAAM,EACN,GAAG,IAAO;IAIV,MAAM,GAAG,GAAG,EAEX,CAAC;IACF,IAAI,GAAqB,CAAC;IAC1B,KAAK,GAAG,IAAI,GAAG,EAAE;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACxB,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;SACpB;KACD;IACD,OAAO,GAAG,CAAC;AACZ,CAAC"}