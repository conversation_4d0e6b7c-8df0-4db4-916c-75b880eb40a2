{"name": "pac-proxy-agent", "version": "7.2.0", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/pac-proxy-agent"}, "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "dependencies": {"@tootallnate/quickjs-emscripten": "^0.23.0", "agent-base": "^7.1.2", "debug": "^4.3.4", "get-uri": "^6.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.6", "pac-resolver": "^7.0.1", "socks-proxy-agent": "^8.0.5"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "jest": "^29.5.0", "socksv5": "0.0.6", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.2.0", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}}