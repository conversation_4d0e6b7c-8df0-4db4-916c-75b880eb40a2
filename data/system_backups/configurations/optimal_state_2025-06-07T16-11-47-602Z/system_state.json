{"timestamp": "2025-06-07T16:11:47.602Z", "version": "2.1.0", "globalState": {"qi": 225, "neurones": 145, "connexions": 717, "efficacite": 95, "temperature": 0.65, "memoryEntries": 100, "lastUpdate": "2025-06-07T16:11:42.562Z"}, "cognitiveState": {"isActive": true, "processingMode": "normal", "lastActivity": "2025-06-07T16:11:42.579Z", "totalProcessed": 0, "agents": {"deepseek": {"id": "deepseek", "name": "Agent <PERSON>", "status": "active", "capabilities": ["deep_reasoning", "code_analysis", "complex_problem_solving", "research"], "lastUsed": "2025-06-07T16:11:42.579Z"}, "principal": {"id": "principal", "name": "Agent Principal <PERSON><PERSON>", "status": "active", "capabilities": ["conversation", "analysis", "memory_access", "problem_solving"], "lastUsed": "2025-06-07T16:11:42.579Z"}}}, "kyberStats": {"accelerators": {"processing": {"enabled": true, "boost": 1.5, "mode": "turbo"}, "memory": {"enabled": true, "boost": 2, "cacheSize": 1000}, "network": {"enabled": true, "boost": 1.8, "compression": true}}, "stats": {"totalAccelerations": 0, "averageBoost": 0, "lastAcceleration": null}, "timestamp": "2025-06-07T16:11:47.602Z"}, "turboStatus": {"enabled": false, "level": 1, "adaptiveMode": true, "lastAdjustment": null, "performance": {"currentLoad": 0, "averageResponseTime": 0, "totalRequests": 0, "successRate": 100}, "thresholds": {"lowLoad": 30, "mediumLoad": 60, "highLoad": 85, "maxResponseTime": 2000, "minResponseTime": 100}, "timestamp": "2025-06-07T16:11:47.602Z"}, "performance": {"uptime": 5.079763542, "memoryUsage": {"rss": 80150528, "heapTotal": 35454976, "heapUsed": 17518296, "external": 3446163, "arrayBuffers": 81066}, "cpuUsage": {"user": 199944, "system": 35524}}}