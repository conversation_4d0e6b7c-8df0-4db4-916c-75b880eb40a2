{"memory": {"usage": {"rss": 90161152, "heapTotal": 38600704, "heapUsed": 23617360, "external": 3473243, "arrayBuffers": 85681}, "uptime": 5.151503875, "timestamp": "2025-06-07T17:09:36.298Z"}, "state": null, "config": null, "logs": null, "crash": {"type": "uncaughtException", "error": "TypeError: global.thermalMemory.addInformation is not a function", "stack": "TypeError: global.thermalMemory.addInformation is not a function\n    at /Volumes/seagate/LOUNA-AI-VIVANTE/server.js:15321:34\n    at Array.forEach (<anonymous>)\n    at forceAgentEvolution (/Volumes/seagate/LOUNA-AI-VIVANTE/server.js:15320:23)\n    at Timeout._onTimeout (/Volumes/seagate/LOUNA-AI-VIVANTE/server.js:15344:5)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-06-07T17:09:36.299Z"}}