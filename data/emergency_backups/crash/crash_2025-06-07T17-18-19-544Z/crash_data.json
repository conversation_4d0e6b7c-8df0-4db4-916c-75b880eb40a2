{"memory": {"usage": {"rss": 69681152, "heapTotal": 25755648, "heapUsed": 24152704, "external": 3454790, "arrayBuffers": 92512}, "uptime": 75.105436167, "timestamp": "2025-06-07T17:18:19.544Z"}, "state": null, "config": null, "logs": null, "crash": {"type": "uncaughtException", "error": "TypeError: logger.security is not a function", "stack": "TypeError: logger.security is not a function\n    at SecuritySystem.<anonymous> (/Volumes/seagate/LOUNA-AI-VIVANTE/server.js:537:12)\n    at SecuritySystem.emit (node:events:507:28)\n    at SecuritySystem.reportThreat (/Volumes/seagate/LOUNA-AI-VIVANTE/security-system.js:443:10)\n    at SecuritySystem.handleSecurityError (/Volumes/seagate/LOUNA-AI-VIVANTE/security-system.js:553:10)\n    at SecuritySystem.performSecurityCheck (/Volumes/seagate/LOUNA-AI-VIVANTE/security-system.js:142:12)\n    at Timeout._onTimeout (/Volumes/seagate/LOUNA-AI-VIVANTE/security-system.js:77:12)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-06-07T17:18:19.546Z"}}