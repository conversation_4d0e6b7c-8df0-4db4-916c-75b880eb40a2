{"memory": {"usage": {"rss": 79593472, "heapTotal": 34930688, "heapUsed": 17763080, "external": 3446963, "arrayBuffers": 81866}, "uptime": 5.099655208, "timestamp": "2025-06-07T16:09:34.453Z"}, "state": null, "config": null, "logs": null, "crash": {"type": "uncaughtException", "error": "TypeError: logger.security is not a function", "stack": "TypeError: logger.security is not a function\n    at SecuritySystem.<anonymous> (/Volumes/seagate/LOUNA-AI-VIVANTE/server.js:537:12)\n    at SecuritySystem.emit (node:events:507:28)\n    at SecuritySystem.reportThreat (/Volumes/seagate/LOUNA-AI-VIVANTE/security-system.js:443:10)\n    at SecuritySystem.handleSecurityError (/Volumes/seagate/LOUNA-AI-VIVANTE/security-system.js:553:10)\n    at SecuritySystem.performSecurityCheck (/Volumes/seagate/LOUNA-AI-VIVANTE/security-system.js:142:12)\n    at Timeout._onTimeout (/Volumes/seagate/LOUNA-AI-VIVANTE/security-system.js:77:12)\n    at listOnTimeout (node:internal/timers:608:17)\n    at process.processTimers (node:internal/timers:543:7)", "timestamp": "2025-06-07T16:09:34.454Z"}}