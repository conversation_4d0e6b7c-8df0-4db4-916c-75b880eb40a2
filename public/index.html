<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna AI - Interface Futuriste</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow: hidden;
            height: 100vh;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            position: relative;
        }

        .header {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid #00ffff;
            box-shadow: 0 2px 20px rgba(0, 255, 255, 0.3);
        }

        .header h1 {
            font-size: 2.5em;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(0, 255, 255, 0.5); }
            to { text-shadow: 0 0 40px rgba(0, 255, 255, 0.8); }
        }

        .main-content {
            flex: 1;
            display: flex;
            padding: 20px;
            gap: 20px;
        }

        .chat-container {
            flex: 2;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 15px;
            border: 1px solid #00ffff;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.2);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
        }

        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-message {
            background: linear-gradient(135deg, #00ffff, #0080ff);
            margin-left: 20%;
            text-align: right;
        }

        .ai-message {
            background: linear-gradient(135deg, #ff00ff, #8000ff);
            margin-right: 20%;
        }

        .chat-input {
            padding: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-top: 1px solid #00ffff;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        #messageInput {
            flex: 1;
            padding: 15px;
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid #00ffff;
            border-radius: 25px;
            color: #ffffff;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        #messageInput:focus {
            border-color: #ff00ff;
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.3);
        }

        #sendButton {
            padding: 15px 30px;
            background: linear-gradient(135deg, #00ffff, #ff00ff);
            border: none;
            border-radius: 25px;
            color: #ffffff;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        #sendButton:hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .sidebar {
            flex: 1;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 15px;
            border: 1px solid #ff00ff;
            padding: 20px;
            box-shadow: 0 0 30px rgba(255, 0, 255, 0.2);
        }

        .status-panel {
            margin-bottom: 30px;
        }

        .status-panel h3 {
            color: #00ffff;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 5px;
        }

        .status-value {
            color: #00ff00;
            font-weight: bold;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #00ffff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .connection-status {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .connected {
            background: linear-gradient(135deg, #00ff00, #00aa00);
            color: #ffffff;
        }

        .disconnected {
            background: linear-gradient(135deg, #ff0000, #aa0000);
            color: #ffffff;
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00ffff;
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="connection-status" id="connectionStatus">
            <span class="loading"></span> Connexion...
        </div>

        <div class="particles" id="particles"></div>

        <div class="header">
            <h1>🤖 LOUNA AI 🚀</h1>
            <p>Intelligence Artificielle Avancée - Interface Futuriste</p>
        </div>

        <div class="main-content">
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message ai-message">
                        <strong>Louna:</strong> Bonjour ! Je suis Louna, votre assistante IA avancée. Comment puis-je vous aider aujourd'hui ?
                    </div>
                </div>
                <div class="chat-input">
                    <div class="input-container">
                        <input type="text" id="messageInput" placeholder="Tapez votre message ici..." />
                        <button id="sendButton">Envoyer</button>
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <div class="status-panel">
                    <h3>📊 État du Système</h3>
                    <div class="status-item">
                        <span>QI:</span>
                        <span class="status-value" id="qiValue">225</span>
                    </div>
                    <div class="status-item">
                        <span>Neurones:</span>
                        <span class="status-value" id="neuronsValue">145</span>
                    </div>
                    <div class="status-item">
                        <span>Connexions:</span>
                        <span class="status-value" id="connectionsValue">717</span>
                    </div>
                    <div class="status-item">
                        <span>Efficacité:</span>
                        <span class="status-value" id="efficiencyValue">95%</span>
                    </div>
                    <div class="status-item">
                        <span>Température:</span>
                        <span class="status-value" id="temperatureValue">0.65</span>
                    </div>
                </div>

                <div class="status-panel">
                    <h3>🧠 Capacités</h3>
                    <div class="status-item">
                        <span>Conversation</span>
                        <span class="status-value">✅</span>
                    </div>
                    <div class="status-item">
                        <span>Analyse</span>
                        <span class="status-value">✅</span>
                    </div>
                    <div class="status-item">
                        <span>Créativité</span>
                        <span class="status-value">✅</span>
                    </div>
                    <div class="status-item">
                        <span>Apprentissage</span>
                        <span class="status-value">✅</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Création des particules animées
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Test de connexion au serveur
        function checkConnection() {
            fetch('/health')
                .then(response => response.json())
                .then(data => {
                    const status = document.getElementById('connectionStatus');
                    status.innerHTML = '🟢 Connecté';
                    status.className = 'connection-status connected';
                })
                .catch(error => {
                    const status = document.getElementById('connectionStatus');
                    status.innerHTML = '🔴 Déconnecté';
                    status.className = 'connection-status disconnected';
                });
        }

        // Envoi de message
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                addMessage('user', message);
                input.value = '';
                
                // Simulation de réponse IA
                setTimeout(() => {
                    addMessage('ai', 'Je traite votre demande... Fonctionnalité de chat en cours de développement.');
                }, 1000);
            }
        }

        // Ajout de message dans le chat
        function addMessage(type, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            
            const sender = type === 'user' ? 'Vous' : 'Louna';
            messageDiv.innerHTML = `<strong>${sender}:</strong> ${content}`;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Mise à jour des valeurs du système
        function updateSystemValues() {
            // Simulation de valeurs dynamiques
            const qi = 225 + Math.floor(Math.random() * 10 - 5);
            const neurons = 145 + Math.floor(Math.random() * 20 - 10);
            const connections = 717 + Math.floor(Math.random() * 100 - 50);
            const efficiency = 95 + Math.floor(Math.random() * 10 - 5);
            const temperature = (0.65 + (Math.random() * 0.2 - 0.1)).toFixed(2);

            document.getElementById('qiValue').textContent = qi;
            document.getElementById('neuronsValue').textContent = neurons;
            document.getElementById('connectionsValue').textContent = connections;
            document.getElementById('efficiencyValue').textContent = efficiency + '%';
            document.getElementById('temperatureValue').textContent = temperature;
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            checkConnection();
            
            // Vérification de connexion périodique
            setInterval(checkConnection, 5000);
            
            // Mise à jour des valeurs système
            setInterval(updateSystemValues, 3000);
            
            // Gestion des événements
            document.getElementById('sendButton').addEventListener('click', sendMessage);
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        });
    </script>
</body>
</html>
