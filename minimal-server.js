/**
 * SERVEUR LOUNA AI MINIMAL - SEULEMENT 2 AGENTS ESSENTIELS
 * Évite les plantages système en limitant les ressources
 */

const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');

// Initialisation de l'application
const app = express();
const PORT = 3005;

// Middleware minimal
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Variables globales minimales
let isInitialized = false;

/**
 * Initialisation minimale
 */
async function initializeMinimalSystem() {
    try {
        console.log('🚀 Initialisation système minimal...');
        
        // Vérifier qu'Ollama fonctionne
        const response = await axios.get('http://localhost:11434/api/version', { timeout: 5000 });
        console.log('✅ Ollama connecté:', response.data.version);
        
        isInitialized = true;
        console.log('✅ Système minimal initialisé !');
        
        return true;
    } catch (error) {
        console.error('❌ Erreur initialisation:', error.message);
        return false;
    }
}

/**
 * Appel simple aux agents Ollama
 */
async function callAgent(model, message) {
    try {
        console.log(`🤖 Appel agent: ${model}`);
        
        const response = await axios.post('http://localhost:11434/api/generate', {
            model: model,
            prompt: message,
            stream: false,
            options: {
                temperature: 0.8,
                top_p: 0.9,
                max_tokens: 2000,
                num_thread: 4  // Limité pour éviter la surcharge
            }
        }, { 
            timeout: 45000  // 45 secondes max
        });
        
        if (response.data && response.data.response) {
            console.log(`✅ Réponse reçue de ${model}`);
            return response.data.response;
        }
        
        return null;
    } catch (error) {
        console.error(`❌ Erreur agent ${model}:`, error.message);
        return null;
    }
}

/**
 * Route de chat standard - Agent Formateur/Gardien DeepSeek R1 (7GB)
 */
app.post('/api/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({
                success: false,
                error: 'Message requis'
            });
        }
        
        console.log('💬 Chat standard - Agent Formateur/Gardien');
        
        const response = await callAgent('deepseek-r1:7b', message);
        
        if (response) {
            res.json({
                success: true,
                response: response,
                agent: 'DeepSeek R1 Formateur/Gardien (7GB)',
                mode: 'standard',
                timestamp: new Date().toISOString()
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Agent non disponible'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur chat:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

/**
 * Route de chat ultra - Agent 19GB CodeLlama
 */
app.post('/api/chat/ultra', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({
                success: false,
                error: 'Message requis'
            });
        }
        
        console.log('⚡ Chat ultra - Agent 19GB');
        
        const response = await callAgent('codellama:34b-instruct', message);
        
        if (response) {
            res.json({
                success: true,
                response: response,
                agent: 'CodeLlama Ultra (19GB)',
                mode: 'ultra',
                timestamp: new Date().toISOString()
            });
        } else {
            // Fallback vers l'agent 7GB
            console.log('🔄 Fallback vers agent 7GB...');
            const fallbackResponse = await callAgent('deepseek-r1:7b', message);
            
            if (fallbackResponse) {
                res.json({
                    success: true,
                    response: fallbackResponse,
                    agent: 'DeepSeek R1 (Fallback)',
                    mode: 'fallback',
                    timestamp: new Date().toISOString()
                });
            } else {
                res.status(500).json({
                    success: false,
                    error: 'Aucun agent disponible'
                });
            }
        }
        
    } catch (error) {
        console.error('❌ Erreur chat ultra:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

/**
 * Route de statut simple
 */
app.get('/api/status', async (req, res) => {
    try {
        // Vérifier Ollama
        let ollamaStatus = 'disconnected';
        try {
            await axios.get('http://localhost:11434/api/version', { timeout: 3000 });
            ollamaStatus = 'connected';
        } catch (error) {
            // Ollama non disponible
        }
        
        res.json({
            success: true,
            status: {
                server: 'running',
                ollama: ollamaStatus,
                initialized: isInitialized
            },
            agents: {
                'deepseek-r1:7b': 'ready',
                'codellama:34b-instruct': 'ready'
            },
            mode: 'minimal',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur statut'
        });
    }
});

/**
 * Route des modèles disponibles
 */
app.get('/api/models', async (req, res) => {
    try {
        const response = await axios.get('http://localhost:11434/api/tags', { timeout: 5000 });
        
        res.json({
            success: true,
            models: response.data.models || [],
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur récupération modèles'
        });
    }
});

/**
 * Route par défaut
 */
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

/**
 * Gestion des erreurs globales
 */
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non gérée:', error.message);
    // Ne pas arrêter le serveur pour éviter les plantages
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée:', reason);
    // Ne pas arrêter le serveur
});

/**
 * Démarrage du serveur minimal
 */
async function startMinimalServer() {
    try {
        // Initialiser le système minimal
        await initializeMinimalSystem();
        
        // Démarrer le serveur
        app.listen(PORT, () => {
            console.log('🎯 ================================');
            console.log('🚀 LOUNA AI - SERVEUR MINIMAL');
            console.log('🎯 ================================');
            console.log(`✅ Serveur démarré sur le port ${PORT}`);
            console.log('🤖 AGENTS DISPONIBLES:');
            console.log('   • DeepSeek R1 7B - Agent Standard');
            console.log('   • CodeLlama 34B - Agent Ultra (19GB)');
            console.log('⚡ MODE: Minimal (évite les plantages)');
            console.log('🌐 URL: http://localhost:3005');
            console.log('🎯 ================================');
        });
        
    } catch (error) {
        console.error('❌ Erreur démarrage serveur:', error.message);
        process.exit(1);
    }
}

// Démarrer le serveur minimal
startMinimalServer();
