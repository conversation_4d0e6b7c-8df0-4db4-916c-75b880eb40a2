/**
 * SERVEUR LOUNA AI MINIMAL - SEULEMENT 2 AGENTS ESSENTIELS
 * Évite les plantages système en limitant les ressources
 */

const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');

// Import de l'intégration Ollama directe
const { ollamaIntegration } = require('./ollama-direct-integration');

// Import de la mémoire thermique vivante (version la plus évoluée)
const RealThermalMemoryEvolution = require('./modules/real-thermal-memory-evolution');

// Initialisation de l'application
const app = express();
const PORT = 3005;

// Middleware minimal
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Variables globales minimales
let thermalMemoryEvolution;
let isInitialized = false;

/**
 * Initialisation minimale avec Ollama intégré
 */
async function initializeMinimalSystem() {
    try {
        console.log('🚀 Initialisation système minimal avec Ollama intégré...');

        // 1. Installer Ollama si nécessaire
        const installed = await ollamaIntegration.installOllamaIfNeeded();
        if (!installed) {
            throw new Error('Installation d\'Ollama échouée');
        }

        // 2. Démarrer Ollama intégré
        const started = await ollamaIntegration.startOllamaIntegrated();
        if (!started) {
            throw new Error('Démarrage d\'Ollama échoué');
        }

        // 3. Initialiser la mémoire thermique vivante (version la plus évoluée)
        thermalMemoryEvolution = new RealThermalMemoryEvolution();
        global.thermalMemoryEvolution = thermalMemoryEvolution;
        console.log('✅ Mémoire thermique vivante initialisée');

        // 4. Vérifier et télécharger les modèles
        const modelsReady = await ollamaIntegration.ensureModelsAvailable();
        if (!modelsReady) {
            console.log('⚠️ Certains modèles ne sont pas disponibles');
        }

        isInitialized = true;
        console.log('✅ Système minimal avec Ollama intégré et mémoire vivante initialisé !');

        return true;
    } catch (error) {
        console.error('❌ Erreur initialisation:', error.message);
        return false;
    }
}

/**
 * Appel aux agents via Ollama intégré
 */
async function callAgent(model, message) {
    try {
        console.log(`🤖 Appel agent intégré: ${model}`);

        const response = await ollamaIntegration.generateResponse(model, message, {
            temperature: 0.8,
            top_p: 0.9,
            max_tokens: 2000,
            num_thread: 4  // Limité pour éviter la surcharge
        });

        if (response) {
            console.log(`✅ Réponse reçue de ${model}`);
            return response;
        }

        return null;
    } catch (error) {
        console.error(`❌ Erreur agent ${model}:`, error.message);
        return null;
    }
}

/**
 * Route de chat standard - Agent Formateur/Gardien DeepSeek R1 (7GB)
 */
app.post('/api/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({
                success: false,
                error: 'Message requis'
            });
        }
        
        console.log('💬 Chat standard - Agent Formateur/Gardien');
        
        // Enrichir le message avec la mémoire thermique vivante
        let enrichedMessage = message;
        if (thermalMemoryEvolution) {
            const evolutionState = thermalMemoryEvolution.getEvolutionState();
            const contextInfo = `[Mémoire Vivante - Génération ${evolutionState.evolution.generation}, Phase: ${evolutionState.evolution.phase}, Température: ${evolutionState.evolution.temperature.toFixed(1)}°C]`;
            enrichedMessage = `${contextInfo}\n\nMessage utilisateur: ${message}`;
        }

        const response = await callAgent('deepseek-r1:7b', enrichedMessage);

        if (response) {
            // Déclencher une évolution de la mémoire thermique
            if (thermalMemoryEvolution) {
                thermalMemoryEvolution.accelerateEvolution(1.2);
            }

            res.json({
                success: true,
                response: response,
                agent: 'DeepSeek R1 Formateur/Gardien (7GB)',
                mode: 'standard-with-living-memory',
                memoryEvolution: thermalMemoryEvolution ? thermalMemoryEvolution.getEvolutionStats() : null,
                timestamp: new Date().toISOString()
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Agent non disponible'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur chat:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

/**
 * Route de chat ultra - Agent 19GB CodeLlama
 */
app.post('/api/chat/ultra', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({
                success: false,
                error: 'Message requis'
            });
        }
        
        console.log('⚡ Chat ultra - Agent 19GB avec mémoire vivante');

        // Enrichir avec l'état complet de la mémoire thermique vivante
        let ultraEnrichedMessage = message;
        if (thermalMemoryEvolution) {
            const evolutionState = thermalMemoryEvolution.getEvolutionState();
            const stats = thermalMemoryEvolution.getEvolutionStats();
            const contextInfo = `[Mémoire Thermique Vivante Ultra - Génération ${stats.currentGeneration}, Phase: ${stats.currentPhase}, Température: ${stats.currentTemperature.toFixed(1)}°C, Complexité: ${evolutionState.evolution.complexity.toFixed(2)}, Adaptabilité: ${evolutionState.evolution.adaptability.toFixed(2)}]`;
            ultraEnrichedMessage = `${contextInfo}\n\nMessage utilisateur: ${message}`;
        }

        const response = await callAgent('codellama:34b-instruct', ultraEnrichedMessage);

        if (response) {
            // Déclencher une évolution accélérée pour l'agent ultra
            if (thermalMemoryEvolution) {
                thermalMemoryEvolution.accelerateEvolution(2.0);
            }

            res.json({
                success: true,
                response: response,
                agent: 'CodeLlama Ultra (19GB)',
                mode: 'ultra-with-living-memory',
                memoryEvolution: thermalMemoryEvolution ? thermalMemoryEvolution.getEvolutionStats() : null,
                timestamp: new Date().toISOString()
            });
        } else {
            // Fallback vers l'agent 7GB
            console.log('🔄 Fallback vers agent 7GB...');
            const fallbackResponse = await callAgent('deepseek-r1:7b', message);
            
            if (fallbackResponse) {
                res.json({
                    success: true,
                    response: fallbackResponse,
                    agent: 'DeepSeek R1 (Fallback)',
                    mode: 'fallback',
                    timestamp: new Date().toISOString()
                });
            } else {
                res.status(500).json({
                    success: false,
                    error: 'Aucun agent disponible'
                });
            }
        }
        
    } catch (error) {
        console.error('❌ Erreur chat ultra:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

/**
 * Route de statut simple
 */
app.get('/api/status', async (req, res) => {
    try {
        const ollamaStatus = ollamaIntegration.getStatus();
        const availableModels = await ollamaIntegration.getAvailableModels();

        res.json({
            success: true,
            status: {
                server: 'running',
                ollama: ollamaStatus.isRunning ? 'integrated' : 'stopped',
                initialized: isInitialized
            },
            ollama: ollamaStatus,
            agents: {
                'deepseek-r1:7b': availableModels.find(m => m.name === 'deepseek-r1:7b') ? 'ready' : 'not_available',
                'codellama:34b-instruct': availableModels.find(m => m.name === 'codellama:34b-instruct') ? 'ready' : 'not_available'
            },
            models: availableModels,
            mode: 'minimal-integrated',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur statut'
        });
    }
});

/**
 * Route des modèles disponibles
 */
app.get('/api/models', async (req, res) => {
    try {
        const models = await ollamaIntegration.getAvailableModels();

        res.json({
            success: true,
            models: models,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur récupération modèles'
        });
    }
});

/**
 * Route de la mémoire thermique vivante
 */
app.get('/api/memory/evolution', (req, res) => {
    try {
        if (!thermalMemoryEvolution) {
            return res.status(404).json({
                success: false,
                error: 'Mémoire thermique vivante non initialisée'
            });
        }

        const evolutionState = thermalMemoryEvolution.getEvolutionState();
        const stats = thermalMemoryEvolution.getEvolutionStats();

        res.json({
            success: true,
            evolution: evolutionState,
            stats: stats,
            isLiving: true,
            version: 'RealThermalMemoryEvolution',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur mémoire thermique vivante'
        });
    }
});

app.post('/api/memory/accelerate', (req, res) => {
    try {
        if (!thermalMemoryEvolution) {
            return res.status(404).json({
                success: false,
                error: 'Mémoire thermique vivante non initialisée'
            });
        }

        const { factor = 2.0 } = req.body;
        thermalMemoryEvolution.accelerateEvolution(factor);

        res.json({
            success: true,
            message: `Évolution accélérée avec facteur ${factor}`,
            newStats: thermalMemoryEvolution.getEvolutionStats(),
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur accélération évolution'
        });
    }
});

/**
 * Route par défaut
 */
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

/**
 * Gestion des erreurs globales et arrêt propre
 */
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non gérée:', error.message);
    // Ne pas arrêter le serveur pour éviter les plantages
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée:', reason);
    // Ne pas arrêter le serveur
});

// Arrêt propre du serveur
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt du serveur...');
    await ollamaIntegration.stopOllama();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Arrêt du serveur...');
    await ollamaIntegration.stopOllama();
    process.exit(0);
});

/**
 * Démarrage du serveur minimal
 */
async function startMinimalServer() {
    try {
        // Initialiser le système minimal
        await initializeMinimalSystem();
        
        // Démarrer le serveur
        app.listen(PORT, () => {
            console.log('🎯 ================================');
            console.log('🚀 LOUNA AI - SERVEUR MINIMAL');
            console.log('🎯 ================================');
            console.log(`✅ Serveur démarré sur le port ${PORT}`);
            console.log('🤖 AGENTS DISPONIBLES:');
            console.log('   • DeepSeek R1 7B - Agent Standard');
            console.log('   • CodeLlama 34B - Agent Ultra (19GB)');
            console.log('⚡ MODE: Minimal (évite les plantages)');
            console.log('🌐 URL: http://localhost:3005');
            console.log('🎯 ================================');
        });
        
    } catch (error) {
        console.error('❌ Erreur démarrage serveur:', error.message);
        process.exit(1);
    }
}

// Démarrer le serveur minimal
startMinimalServer();
