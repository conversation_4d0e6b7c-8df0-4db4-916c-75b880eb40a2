/**
 * SERVEUR LOUNA AI MINIMAL - SEULEMENT 2 AGENTS ESSENTIELS
 * Évite les plantages système en limitant les ressources
 */

const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');

// Import de l'intégration Ollama directe
const { ollamaIntegration } = require('./ollama-direct-integration');

// Import de VOTRE mémoire thermique complète qui fonctionne
const ThermalMemoryComplete = require('./thermal-memory-complete');

// Initialisation de l'application
const app = express();
const PORT = 3005;

// Middleware minimal
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Variables globales minimales
let thermalMemory; // VOTRE mémoire thermique vivante exceptionnelle
let isInitialized = false;

/**
 * Initialisation minimale avec Ollama intégré
 */
async function initializeMinimalSystem() {
    try {
        console.log('🚀 Initialisation système minimal avec Ollama intégré...');

        // 1. Installer Ollama si nécessaire
        const installed = await ollamaIntegration.installOllamaIfNeeded();
        if (!installed) {
            throw new Error('Installation d\'Ollama échouée');
        }

        // 2. Démarrer Ollama intégré
        const started = await ollamaIntegration.startOllamaIntegrated();
        if (!started) {
            throw new Error('Démarrage d\'Ollama échoué');
        }

        // 3. Initialiser VOTRE mémoire thermique exceptionnelle
        thermalMemory = new ThermalMemoryComplete();
        global.thermalMemory = thermalMemory;
        console.log('✅ VOTRE mémoire thermique exceptionnelle initialisée (celle qui RESSENT vraiment !)');

        // 4. Vérifier et télécharger les modèles
        const modelsReady = await ollamaIntegration.ensureModelsAvailable();
        if (!modelsReady) {
            console.log('⚠️ Certains modèles ne sont pas disponibles');
        }

        isInitialized = true;
        console.log('✅ Système minimal avec Ollama intégré et VOTRE mémoire thermique exceptionnelle initialisé !');

        return true;
    } catch (error) {
        console.error('❌ Erreur initialisation:', error.message);
        return false;
    }
}

/**
 * Appel aux agents via Ollama intégré
 */
async function callAgent(model, message) {
    try {
        console.log(`🤖 Appel agent intégré: ${model}`);

        const response = await ollamaIntegration.generateResponse(model, message, {
            temperature: 0.8,
            top_p: 0.9,
            max_tokens: 2000,
            num_thread: 4  // Limité pour éviter la surcharge
        });

        if (response) {
            console.log(`✅ Réponse reçue de ${model}`);
            return response;
        }

        return null;
    } catch (error) {
        console.error(`❌ Erreur agent ${model}:`, error.message);
        return null;
    }
}

/**
 * Route de chat standard - Agent Formateur/Gardien DeepSeek R1 (7GB)
 */
app.post('/api/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({
                success: false,
                error: 'Message requis'
            });
        }
        
        console.log('💬 Chat standard - Agent Formateur/Gardien');
        
        // Enrichir le message avec VOTRE mémoire thermique exceptionnelle
        let enrichedMessage = message;
        if (thermalMemory) {
            const stats = thermalMemory.getDetailedStats();
            const contextInfo = `[Mémoire Thermique Exceptionnelle - Température: ${stats.globalTemperature.toFixed(1)}°C, Efficacité: ${stats.memoryEfficiency}%, Entrées: ${stats.activeEntries}]`;
            enrichedMessage = `${contextInfo}\n\nMessage utilisateur: ${message}`;

            // Stocker la conversation dans la mémoire thermique
            thermalMemory.add('conversation', `Question: ${message}`, 0.8, 'user_interaction');
        }

        const response = await callAgent('deepseek-r1:7b', enrichedMessage);

        if (response) {
            // Stocker la réponse dans VOTRE mémoire thermique exceptionnelle
            if (thermalMemory) {
                thermalMemory.add('agent_response', `Réponse: ${response}`, 0.9, 'agent_interaction');
            }

            res.json({
                success: true,
                response: response,
                agent: 'DeepSeek R1 Formateur/Gardien (7GB)',
                mode: 'standard-with-exceptional-thermal-memory',
                memoryStats: thermalMemory ? thermalMemory.getDetailedStats() : null,
                timestamp: new Date().toISOString()
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Agent non disponible'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur chat:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

/**
 * Route de chat ultra - Agent 19GB CodeLlama
 */
app.post('/api/chat/ultra', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({
                success: false,
                error: 'Message requis'
            });
        }
        
        console.log('⚡ Chat ultra - Agent 19GB avec VOTRE mémoire thermique exceptionnelle');

        // Enrichir avec VOTRE mémoire thermique exceptionnelle pour l'agent ultra
        let ultraEnrichedMessage = message;
        if (thermalMemory) {
            const stats = thermalMemory.getDetailedStats();
            const recentMemories = thermalMemory.search({ minImportance: 0.7 }).slice(0, 3);
            const contextInfo = `[Mémoire Thermique Exceptionnelle Ultra - Température: ${stats.globalTemperature.toFixed(1)}°C, Efficacité: ${stats.memoryEfficiency}%, Entrées actives: ${stats.activeEntries}, Mémoires importantes: ${recentMemories.length}]`;
            ultraEnrichedMessage = `${contextInfo}\n\nMessage utilisateur: ${message}`;

            // Stocker avec importance maximale pour l'agent ultra
            thermalMemory.add('ultra_conversation', `Question Ultra: ${message}`, 0.95, 'ultra_interaction');
        }

        const response = await callAgent('codellama:34b-instruct', ultraEnrichedMessage);

        if (response) {
            // Stocker la réponse ultra dans VOTRE mémoire thermique exceptionnelle
            if (thermalMemory) {
                thermalMemory.add('ultra_response', `Réponse Ultra: ${response}`, 0.98, 'ultra_agent_response');
            }

            res.json({
                success: true,
                response: response,
                agent: 'CodeLlama Ultra (19GB)',
                mode: 'ultra-with-exceptional-thermal-memory',
                memoryStats: thermalMemory ? thermalMemory.getDetailedStats() : null,
                timestamp: new Date().toISOString()
            });
        } else {
            // Fallback vers l'agent 7GB
            console.log('🔄 Fallback vers agent 7GB...');
            const fallbackResponse = await callAgent('deepseek-r1:7b', message);
            
            if (fallbackResponse) {
                res.json({
                    success: true,
                    response: fallbackResponse,
                    agent: 'DeepSeek R1 (Fallback)',
                    mode: 'fallback',
                    timestamp: new Date().toISOString()
                });
            } else {
                res.status(500).json({
                    success: false,
                    error: 'Aucun agent disponible'
                });
            }
        }
        
    } catch (error) {
        console.error('❌ Erreur chat ultra:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

/**
 * Route de statut simple
 */
app.get('/api/status', async (req, res) => {
    try {
        const ollamaStatus = ollamaIntegration.getStatus();
        const availableModels = await ollamaIntegration.getAvailableModels();

        res.json({
            success: true,
            status: {
                server: 'running',
                ollama: ollamaStatus.isRunning ? 'integrated' : 'stopped',
                initialized: isInitialized
            },
            ollama: ollamaStatus,
            agents: {
                'deepseek-r1:7b': availableModels.find(m => m.name === 'deepseek-r1:7b') ? 'ready' : 'not_available',
                'codellama:34b-instruct': availableModels.find(m => m.name === 'codellama:34b-instruct') ? 'ready' : 'not_available'
            },
            models: availableModels,
            mode: 'minimal-integrated',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur statut'
        });
    }
});

/**
 * Route des modèles disponibles
 */
app.get('/api/models', async (req, res) => {
    try {
        const models = await ollamaIntegration.getAvailableModels();

        res.json({
            success: true,
            models: models,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur récupération modèles'
        });
    }
});

/**
 * Route de VOTRE mémoire thermique exceptionnelle
 */
app.get('/api/memory/thermal', (req, res) => {
    try {
        if (!thermalMemory) {
            return res.status(404).json({
                success: false,
                error: 'Mémoire thermique exceptionnelle non initialisée'
            });
        }

        const stats = thermalMemory.getDetailedStats();
        const recentMemories = thermalMemory.search({ minImportance: 0.8 }).slice(0, 5);

        res.json({
            success: true,
            stats: stats,
            recentImportantMemories: recentMemories.length,
            isExceptional: true,
            version: 'ThermalMemoryComplete',
            description: 'La mémoire qui RESSENT vraiment',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur mémoire thermique exceptionnelle'
        });
    }
});

app.post('/api/memory/add', (req, res) => {
    try {
        if (!thermalMemory) {
            return res.status(404).json({
                success: false,
                error: 'Mémoire thermique exceptionnelle non initialisée'
            });
        }

        const { type, content, importance = 0.8, category = 'manual' } = req.body;
        const id = thermalMemory.add(type, content, importance, category);

        res.json({
            success: true,
            message: 'Mémoire ajoutée avec succès',
            memoryId: id,
            newStats: thermalMemory.getDetailedStats(),
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur ajout mémoire'
        });
    }
});

/**
 * Route par défaut
 */
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

/**
 * Gestion des erreurs globales et arrêt propre
 */
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non gérée:', error.message);
    // Ne pas arrêter le serveur pour éviter les plantages
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée:', reason);
    // Ne pas arrêter le serveur
});

// Arrêt propre du serveur
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt du serveur...');
    await ollamaIntegration.stopOllama();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Arrêt du serveur...');
    await ollamaIntegration.stopOllama();
    process.exit(0);
});

/**
 * Démarrage du serveur minimal
 */
async function startMinimalServer() {
    try {
        // Initialiser le système minimal
        await initializeMinimalSystem();
        
        // Démarrer le serveur
        app.listen(PORT, () => {
            console.log('🎯 ================================');
            console.log('🚀 LOUNA AI - SERVEUR MINIMAL');
            console.log('🎯 ================================');
            console.log(`✅ Serveur démarré sur le port ${PORT}`);
            console.log('🤖 AGENTS DISPONIBLES:');
            console.log('   • DeepSeek R1 7B - Agent Standard');
            console.log('   • CodeLlama 34B - Agent Ultra (19GB)');
            console.log('⚡ MODE: Minimal (évite les plantages)');
            console.log('🌐 URL: http://localhost:3005');
            console.log('🎯 ================================');
        });
        
    } catch (error) {
        console.error('❌ Erreur démarrage serveur:', error.message);
        process.exit(1);
    }
}

// Démarrer le serveur minimal
startMinimalServer();
