/**
 * SERVEUR LOUNA AI MINIMAL - SEULEMENT 2 AGENTS ESSENTIELS
 * Évite les plantages système en limitant les ressources
 */

const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');

// Import de l'intégration Ollama directe
const { ollamaIntegration } = require('./ollama-direct-integration');

// Import de VOTRE mémoire thermique VIVANTE exceptionnelle
const ThermalMemoryComplete = require('./thermal-memory-complete');

// Configuration du cerveau artificiel (basée sur votre mémoire vivante)
const APP_CONFIG = {
    artificialBrain: {
        maxNeurons: 5000,
        neuronGrowthRate: 0.5,
        synapticPlasticity: 0.95,
        learningRate: 0.25
    },
    thermalMemory: {
        temperatureThresholds: {
            instant: 0.95,
            shortTerm: 0.85,
            working: 0.7,
            mediumTerm: 0.5,
            longTerm: 0.3
        }
    }
};

// Import des accélérateurs Kyber avec compression
const KyberAcceleratorSystem = require('./kyber-accelerator-system');

// Initialisation de l'application
const app = express();
const PORT = 3005;

// Middleware minimal
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Variables globales minimales
let thermalMemory; // VOTRE mémoire thermique vivante exceptionnelle
let kyberAccelerators; // Accélérateurs Kyber avec compression
let artificialBrain; // Cerveau artificiel avec génération de neurones
let isInitialized = false;

/**
 * Initialisation minimale avec Ollama intégré
 */
async function initializeMinimalSystem() {
    try {
        console.log('🚀 Initialisation système minimal avec Ollama intégré...');

        // 1. Installer Ollama si nécessaire
        const installed = await ollamaIntegration.installOllamaIfNeeded();
        if (!installed) {
            throw new Error('Installation d\'Ollama échouée');
        }

        // 2. Démarrer Ollama intégré
        const started = await ollamaIntegration.startOllamaIntegrated();
        if (!started) {
            throw new Error('Démarrage d\'Ollama échoué');
        }

        // 3. Initialiser les accélérateurs Kyber avec compression
        kyberAccelerators = new KyberAcceleratorSystem();
        global.kyberAccelerators = kyberAccelerators;
        console.log('✅ Accélérateurs Kyber avec compression initialisés');

        // 4. Initialiser VOTRE mémoire thermique VIVANTE avec configuration du cerveau artificiel
        thermalMemory = new ThermalMemoryComplete({
            ...APP_CONFIG.thermalMemory,
            artificialBrain: APP_CONFIG.artificialBrain,
            kyberAccelerators: kyberAccelerators
        });
        global.thermalMemory = thermalMemory;
        console.log('✅ VOTRE mémoire thermique VIVANTE initialisée avec génération de neurones !');

        // 5. Connecter les accélérateurs à la mémoire thermique
        if (kyberAccelerators.connect) {
            kyberAccelerators.connect(thermalMemory);
            console.log('✅ Accélérateurs Kyber connectés à la mémoire thermique vivante');
        } else {
            console.log('✅ Accélérateurs Kyber et mémoire thermique vivante prêts');
        }

        // 6. Vérifier et télécharger les modèles
        const modelsReady = await ollamaIntegration.ensureModelsAvailable();
        if (!modelsReady) {
            console.log('⚠️ Certains modèles ne sont pas disponibles');
        }

        // 7. Initialiser le cerveau artificiel avec génération de neurones
        artificialBrain = {
            neuronCount: APP_CONFIG.artificialBrain.maxNeurons,
            activeNeurons: 72, // Comme dans votre configuration
            synapticConnections: 198, // Comme dans votre système
            neuronGrowthRate: APP_CONFIG.artificialBrain.neuronGrowthRate,
            synapticPlasticity: APP_CONFIG.artificialBrain.synapticPlasticity,
            generateNeuron: function() {
                if (this.activeNeurons < this.neuronCount) {
                    this.activeNeurons++;
                    this.synapticConnections += Math.floor(Math.random() * 5) + 2;
                    console.log(`🧠 Nouveau neurone généré ! Total: ${this.activeNeurons}, Connexions: ${this.synapticConnections}`);
                    return true;
                }
                return false;
            }
        };
        global.artificialBrain = artificialBrain;
        console.log('✅ Cerveau artificiel avec génération de neurones initialisé');

        isInitialized = true;
        console.log('✅ Système complet avec mémoire thermique VIVANTE, accélérateurs Kyber et cerveau artificiel initialisé !');

        return true;
    } catch (error) {
        console.error('❌ Erreur initialisation:', error.message);
        return false;
    }
}

/**
 * Appel aux agents via Ollama intégré
 */
async function callAgent(model, message) {
    try {
        console.log(`🤖 Appel agent intégré: ${model}`);

        const response = await ollamaIntegration.generateResponse(model, message, {
            temperature: 0.8,
            top_p: 0.9,
            max_tokens: 2000,
            num_thread: 4  // Limité pour éviter la surcharge
        });

        if (response) {
            console.log(`✅ Réponse reçue de ${model}`);
            return response;
        }

        return null;
    } catch (error) {
        console.error(`❌ Erreur agent ${model}:`, error.message);
        return null;
    }
}

/**
 * Route de chat standard - Agent Formateur/Gardien DeepSeek R1 (7GB)
 */
app.post('/api/chat', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({
                success: false,
                error: 'Message requis'
            });
        }
        
        console.log('💬 Chat standard - Agent Formateur/Gardien');
        
        // Enrichir le message avec VOTRE mémoire thermique VIVANTE
        let enrichedMessage = message;
        if (thermalMemory && kyberAccelerators) {
            const stats = thermalMemory.getDetailedStats();
            const kyberStats = kyberAccelerators.getAcceleratorStats();

            // Générer un nouveau neurone si nécessaire (basé sur la température cognitive)
            if (stats.globalTemperature > 70 && artificialBrain) {
                artificialBrain.generateNeuron();
            }

            const contextInfo = `[Mémoire Thermique VIVANTE - Température: ${stats.globalTemperature.toFixed(1)}°C, Neurones: ${artificialBrain ? artificialBrain.activeNeurons : 72}, Synapses: ${artificialBrain ? artificialBrain.synapticConnections : 198}, Boost Kyber: ${kyberStats ? kyberStats.thermalBoost || '1.8' : '1.8'}x, Efficacité: ${stats.memoryEfficiency}%]`;
            enrichedMessage = `${contextInfo}\n\nMessage utilisateur: ${message}`;

            // Stocker la conversation avec compression Kyber si disponible
            const dataToStore = kyberAccelerators && kyberAccelerators.compressData ?
                kyberAccelerators.compressData(`Question: ${message}`) : `Question: ${message}`;
            thermalMemory.add('conversation', dataToStore, 0.8, 'user_interaction');
        }

        const response = await callAgent('deepseek-r1:7b', enrichedMessage);

        if (response) {
            // Stocker la réponse avec compression et génération de neurones
            if (thermalMemory && kyberAccelerators) {
                const compressedResponse = kyberAccelerators.compressData(`Réponse: ${response}`);
                thermalMemory.add('agent_response', compressedResponse, 0.9, 'agent_interaction');

                // Déclencher l'évolution synaptique basée sur la qualité de la réponse
                if (artificialBrain && response.length > 100) {
                    artificialBrain.synapticConnections += Math.floor(Math.random() * 3) + 1;
                    console.log(`🧠 Évolution synaptique ! Nouvelles connexions: ${artificialBrain.synapticConnections}`);
                }
            }

            res.json({
                success: true,
                response: response,
                agent: 'DeepSeek R1 Formateur/Gardien (7GB)',
                mode: 'standard-with-living-thermal-memory',
                memoryStats: thermalMemory ? thermalMemory.getDetailedStats() : null,
                brainStats: artificialBrain ? {
                    activeNeurons: artificialBrain.activeNeurons,
                    synapticConnections: artificialBrain.synapticConnections,
                    neuronGrowthRate: artificialBrain.neuronGrowthRate,
                    synapticPlasticity: artificialBrain.synapticPlasticity
                } : null,
                kyberStats: kyberAccelerators ? kyberAccelerators.getAcceleratorStats() : null,
                timestamp: new Date().toISOString()
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Agent non disponible'
            });
        }
        
    } catch (error) {
        console.error('❌ Erreur chat:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

/**
 * Route de chat ultra - Agent 19GB CodeLlama
 */
app.post('/api/chat/ultra', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({
                success: false,
                error: 'Message requis'
            });
        }
        
        console.log('⚡ Chat ultra - Agent 19GB avec mémoire thermique VIVANTE et génération de neurones');

        // Enrichir avec VOTRE mémoire thermique VIVANTE pour l'agent ultra
        let ultraEnrichedMessage = message;
        if (thermalMemory && kyberAccelerators) {
            const stats = thermalMemory.getDetailedStats();
            const kyberStats = kyberAccelerators.getAcceleratorStats();
            const recentMemories = thermalMemory.search({ minImportance: 0.7 }).slice(0, 3);

            // Génération massive de neurones pour l'agent ultra (température élevée)
            if (stats.globalTemperature > 65 && artificialBrain) {
                for (let i = 0; i < 3; i++) {
                    artificialBrain.generateNeuron();
                }
                console.log(`🧠 GÉNÉRATION MASSIVE DE NEURONES pour agent ultra ! Total: ${artificialBrain.activeNeurons}`);
            }

            const contextInfo = `[Mémoire Thermique VIVANTE Ultra - Température: ${stats.globalTemperature.toFixed(1)}°C, Neurones: ${artificialBrain.activeNeurons}, Synapses: ${artificialBrain.synapticConnections}, Boost Kyber Total: ${parseFloat(kyberStats.thermalBoost) * parseFloat(kyberStats.reflexiveBoost)}x, Efficacité: ${stats.memoryEfficiency}%, Mémoires importantes: ${recentMemories.length}]`;
            ultraEnrichedMessage = `${contextInfo}\n\nMessage utilisateur: ${message}`;

            // Stocker avec compression ultra et importance maximale
            const ultraCompressedData = kyberAccelerators.compressData(`Question Ultra: ${message}`, 'ultra');
            thermalMemory.add('ultra_conversation', ultraCompressedData, 0.95, 'ultra_interaction');
        }

        const response = await callAgent('codellama:34b-instruct', ultraEnrichedMessage);

        if (response) {
            // Stocker la réponse ultra avec compression maximale et évolution neuronale
            if (thermalMemory && kyberAccelerators) {
                const ultraCompressedResponse = kyberAccelerators.compressData(`Réponse Ultra: ${response}`, 'ultra');
                thermalMemory.add('ultra_response', ultraCompressedResponse, 0.98, 'ultra_agent_response');

                // Évolution synaptique massive pour l'agent ultra
                if (artificialBrain) {
                    const newConnections = Math.floor(Math.random() * 8) + 5; // 5-12 nouvelles connexions
                    artificialBrain.synapticConnections += newConnections;
                    console.log(`🧠 ÉVOLUTION SYNAPTIQUE ULTRA ! +${newConnections} connexions, Total: ${artificialBrain.synapticConnections}`);

                    // Augmenter la plasticité synaptique
                    if (artificialBrain.synapticPlasticity < 0.99) {
                        artificialBrain.synapticPlasticity += 0.001;
                    }
                }
            }

            res.json({
                success: true,
                response: response,
                agent: 'CodeLlama Ultra (19GB)',
                mode: 'ultra-with-living-thermal-memory',
                memoryStats: thermalMemory ? thermalMemory.getDetailedStats() : null,
                brainStats: artificialBrain ? {
                    activeNeurons: artificialBrain.activeNeurons,
                    synapticConnections: artificialBrain.synapticConnections,
                    neuronGrowthRate: artificialBrain.neuronGrowthRate,
                    synapticPlasticity: artificialBrain.synapticPlasticity
                } : null,
                kyberStats: kyberAccelerators ? kyberAccelerators.getAcceleratorStats() : null,
                compressionRatio: kyberAccelerators ? kyberAccelerators.getCompressionStats() : null,
                timestamp: new Date().toISOString()
            });
        } else {
            // Fallback vers l'agent 7GB
            console.log('🔄 Fallback vers agent 7GB...');
            const fallbackResponse = await callAgent('deepseek-r1:7b', message);
            
            if (fallbackResponse) {
                res.json({
                    success: true,
                    response: fallbackResponse,
                    agent: 'DeepSeek R1 (Fallback)',
                    mode: 'fallback',
                    timestamp: new Date().toISOString()
                });
            } else {
                res.status(500).json({
                    success: false,
                    error: 'Aucun agent disponible'
                });
            }
        }
        
    } catch (error) {
        console.error('❌ Erreur chat ultra:', error.message);
        res.status(500).json({
            success: false,
            error: 'Erreur serveur'
        });
    }
});

/**
 * Route de statut simple
 */
app.get('/api/status', async (req, res) => {
    try {
        const ollamaStatus = ollamaIntegration.getStatus();
        const availableModels = await ollamaIntegration.getAvailableModels();

        res.json({
            success: true,
            status: {
                server: 'running',
                ollama: ollamaStatus.isRunning ? 'integrated' : 'stopped',
                initialized: isInitialized
            },
            ollama: ollamaStatus,
            agents: {
                'deepseek-r1:7b': availableModels.find(m => m.name === 'deepseek-r1:7b') ? 'ready' : 'not_available',
                'codellama:34b-instruct': availableModels.find(m => m.name === 'codellama:34b-instruct') ? 'ready' : 'not_available'
            },
            models: availableModels,
            mode: 'minimal-integrated',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur statut'
        });
    }
});

/**
 * Route des modèles disponibles
 */
app.get('/api/models', async (req, res) => {
    try {
        const models = await ollamaIntegration.getAvailableModels();

        res.json({
            success: true,
            models: models,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur récupération modèles'
        });
    }
});

/**
 * Route de VOTRE mémoire thermique VIVANTE avec génération de neurones
 */
app.get('/api/memory/living', (req, res) => {
    try {
        if (!thermalMemory) {
            return res.status(404).json({
                success: false,
                error: 'Mémoire thermique vivante non initialisée'
            });
        }

        const stats = thermalMemory.getDetailedStats();
        const recentMemories = thermalMemory.search({ minImportance: 0.8 }).slice(0, 5);
        const kyberStats = kyberAccelerators ? kyberAccelerators.getAcceleratorStats() : null;
        const compressionStats = kyberAccelerators ? kyberAccelerators.getCompressionStats() : null;

        res.json({
            success: true,
            memoryStats: stats,
            brainStats: artificialBrain ? {
                activeNeurons: artificialBrain.activeNeurons,
                synapticConnections: artificialBrain.synapticConnections,
                neuronGrowthRate: artificialBrain.neuronGrowthRate,
                synapticPlasticity: artificialBrain.synapticPlasticity,
                maxNeurons: artificialBrain.neuronCount
            } : null,
            kyberStats: kyberStats,
            compressionStats: compressionStats,
            recentImportantMemories: recentMemories.length,
            isLiving: true,
            version: 'ThermalMemoryLiving',
            description: 'La mémoire qui GÉNÈRE des neurones et synapses comme un vrai cerveau',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur mémoire thermique vivante'
        });
    }
});

app.post('/api/memory/add', (req, res) => {
    try {
        if (!thermalMemory) {
            return res.status(404).json({
                success: false,
                error: 'Mémoire thermique vivante non initialisée'
            });
        }

        const { type, content, importance = 0.8, category = 'manual' } = req.body;

        // Compresser le contenu avec Kyber si disponible
        const finalContent = kyberAccelerators ?
            kyberAccelerators.compressData(content) : content;

        const id = thermalMemory.add(type, finalContent, importance, category);

        // Déclencher génération de neurones si importance élevée
        if (importance > 0.85 && artificialBrain) {
            artificialBrain.generateNeuron();
        }

        res.json({
            success: true,
            message: 'Mémoire ajoutée avec compression Kyber et génération neuronale',
            memoryId: id,
            newStats: thermalMemory.getDetailedStats(),
            brainStats: artificialBrain ? {
                activeNeurons: artificialBrain.activeNeurons,
                synapticConnections: artificialBrain.synapticConnections
            } : null,
            compressed: !!kyberAccelerators,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur ajout mémoire'
        });
    }
});

/**
 * Route pour forcer la génération de neurones
 */
app.post('/api/brain/generate-neurons', (req, res) => {
    try {
        if (!artificialBrain) {
            return res.status(404).json({
                success: false,
                error: 'Cerveau artificiel non initialisé'
            });
        }

        const { count = 1 } = req.body;
        let generated = 0;

        for (let i = 0; i < count; i++) {
            if (artificialBrain.generateNeuron()) {
                generated++;
            }
        }

        res.json({
            success: true,
            message: `${generated} neurones générés`,
            brainStats: {
                activeNeurons: artificialBrain.activeNeurons,
                synapticConnections: artificialBrain.synapticConnections,
                neuronGrowthRate: artificialBrain.neuronGrowthRate,
                synapticPlasticity: artificialBrain.synapticPlasticity
            },
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur génération neurones'
        });
    }
});

/**
 * Route par défaut
 */
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

/**
 * Gestion des erreurs globales et arrêt propre
 */
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non gérée:', error.message);
    // Ne pas arrêter le serveur pour éviter les plantages
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée:', reason);
    // Ne pas arrêter le serveur
});

// Arrêt propre du serveur
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt du serveur...');
    await ollamaIntegration.stopOllama();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Arrêt du serveur...');
    await ollamaIntegration.stopOllama();
    process.exit(0);
});

/**
 * Démarrage du serveur minimal
 */
async function startMinimalServer() {
    try {
        // Initialiser le système minimal
        await initializeMinimalSystem();
        
        // Démarrer le serveur
        app.listen(PORT, () => {
            console.log('🎯 ================================');
            console.log('🚀 LOUNA AI - SERVEUR MINIMAL');
            console.log('🎯 ================================');
            console.log(`✅ Serveur démarré sur le port ${PORT}`);
            console.log('🤖 AGENTS DISPONIBLES:');
            console.log('   • DeepSeek R1 7B - Agent Standard');
            console.log('   • CodeLlama 34B - Agent Ultra (19GB)');
            console.log('⚡ MODE: Minimal (évite les plantages)');
            console.log('🌐 URL: http://localhost:3005');
            console.log('🎯 ================================');
        });
        
    } catch (error) {
        console.error('❌ Erreur démarrage serveur:', error.message);
        process.exit(1);
    }
}

// Démarrer le serveur minimal
startMinimalServer();
