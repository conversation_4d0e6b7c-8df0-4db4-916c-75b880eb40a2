/**
 * INTÉGRATION DIRECTE D'OLLAMA DANS L'APPLICATION
 * Évite les dépendances externes et les plantages
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const axios = require('axios');

class OllamaDirectIntegration {
    constructor() {
        this.ollamaProcess = null;
        this.isRunning = false;
        this.port = 11434;
        this.baseUrl = `http://localhost:${this.port}`;
        // Utiliser le chemin par défaut d'Ollama où les modèles existent déjà
        this.modelsPath = null; // Laisser Ollama utiliser son chemin par défaut
        this.ollamaPath = null;
        
        console.log('🤖 Intégration directe Ollama initialisée');
    }

    /**
     * Détecte l'installation d'Ollama
     */
    async detectOllamaInstallation() {
        try {
            // Vérifier les chemins communs d'Ollama
            const possiblePaths = [
                '/usr/local/bin/ollama',
                '/opt/homebrew/bin/ollama',
                '/usr/bin/ollama',
                path.join(process.env.HOME, '.ollama', 'bin', 'ollama'),
                'ollama' // Dans le PATH
            ];

            for (const ollamaPath of possiblePaths) {
                try {
                    const { stdout } = await this.execPromise(`${ollamaPath} --version`);
                    if (stdout.includes('ollama version')) {
                        this.ollamaPath = ollamaPath;
                        console.log(`✅ Ollama trouvé: ${ollamaPath}`);
                        return true;
                    }
                } catch (error) {
                    // Continuer la recherche
                }
            }

            console.log('❌ Ollama non trouvé dans les chemins standards');
            return false;
        } catch (error) {
            console.error('❌ Erreur détection Ollama:', error.message);
            return false;
        }
    }

    /**
     * Installe Ollama si nécessaire
     */
    async installOllamaIfNeeded() {
        try {
            const isInstalled = await this.detectOllamaInstallation();
            
            if (!isInstalled) {
                console.log('📦 Installation d\'Ollama...');
                
                // Installation via curl (méthode officielle)
                const installCommand = 'curl -fsSL https://ollama.ai/install.sh | sh';
                await this.execPromise(installCommand);
                
                // Re-vérifier l'installation
                const isNowInstalled = await this.detectOllamaInstallation();
                if (!isNowInstalled) {
                    throw new Error('Installation d\'Ollama échouée');
                }
                
                console.log('✅ Ollama installé avec succès');
            }
            
            return true;
        } catch (error) {
            console.error('❌ Erreur installation Ollama:', error.message);
            return false;
        }
    }

    /**
     * Démarre Ollama en mode intégré
     */
    async startOllamaIntegrated() {
        try {
            if (this.isRunning) {
                console.log('⚠️ Ollama déjà en cours d\'exécution');
                return true;
            }

            console.log('🚀 Démarrage d\'Ollama intégré...');

            // Configurer les variables d'environnement (sans changer le chemin des modèles)
            const env = {
                ...process.env,
                OLLAMA_HOST: `0.0.0.0:${this.port}`,
                // Ne pas définir OLLAMA_MODELS pour utiliser le chemin par défaut
                OLLAMA_NUM_PARALLEL: '2', // Limiter pour éviter la surcharge
                OLLAMA_MAX_LOADED_MODELS: '2',
                OLLAMA_FLASH_ATTENTION: '1'
            };

            // Démarrer Ollama
            this.ollamaProcess = spawn(this.ollamaPath, ['serve'], {
                env: env,
                stdio: ['pipe', 'pipe', 'pipe'],
                detached: false
            });

            // Gérer les événements
            this.ollamaProcess.stdout.on('data', (data) => {
                console.log(`[Ollama] ${data.toString().trim()}`);
            });

            this.ollamaProcess.stderr.on('data', (data) => {
                console.log(`[Ollama Error] ${data.toString().trim()}`);
            });

            this.ollamaProcess.on('close', (code) => {
                console.log(`[Ollama] Processus fermé avec le code ${code}`);
                this.isRunning = false;
                this.ollamaProcess = null;
            });

            this.ollamaProcess.on('error', (error) => {
                console.error(`[Ollama] Erreur processus:`, error.message);
                this.isRunning = false;
            });

            // Attendre que le serveur soit prêt
            await this.waitForOllamaReady();
            
            this.isRunning = true;
            console.log('✅ Ollama intégré démarré avec succès');
            
            return true;
        } catch (error) {
            console.error('❌ Erreur démarrage Ollama:', error.message);
            return false;
        }
    }

    /**
     * Attend qu'Ollama soit prêt
     */
    async waitForOllamaReady(maxAttempts = 30) {
        for (let i = 0; i < maxAttempts; i++) {
            try {
                const response = await axios.get(`${this.baseUrl}/api/version`, { timeout: 2000 });
                if (response.status === 200) {
                    console.log('✅ Ollama prêt');
                    return true;
                }
            } catch (error) {
                // Attendre et réessayer
                await this.sleep(1000);
            }
        }
        
        throw new Error('Ollama n\'a pas démarré dans les temps');
    }

    /**
     * Vérifie les modèles existants (sans re-télécharger)
     */
    async ensureModelsAvailable() {
        try {
            console.log('📋 Vérification des modèles existants...');

            const requiredModels = [
                'deepseek-r1:7b',
                'codellama:34b-instruct'
            ];

            const availableModels = await this.getAvailableModels();
            const availableModelNames = availableModels.map(m => m.name);

            for (const modelName of requiredModels) {
                if (availableModelNames.includes(modelName)) {
                    console.log(`✅ Modèle disponible: ${modelName}`);
                } else {
                    console.log(`⚠️ Modèle manquant: ${modelName}`);
                }
            }

            return true;
        } catch (error) {
            console.error('❌ Erreur vérification modèles:', error.message);
            return false;
        }
    }

    /**
     * Télécharge un modèle
     */
    async pullModel(modelName) {
        try {
            console.log(`📥 Téléchargement de ${modelName}...`);
            
            const response = await axios.post(`${this.baseUrl}/api/pull`, {
                name: modelName
            }, {
                timeout: 600000 // 10 minutes
            });

            console.log(`✅ Modèle ${modelName} téléchargé`);
            return true;
        } catch (error) {
            console.error(`❌ Erreur téléchargement ${modelName}:`, error.message);
            return false;
        }
    }

    /**
     * Obtient la liste des modèles disponibles
     */
    async getAvailableModels() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tags`);
            return response.data.models || [];
        } catch (error) {
            console.error('❌ Erreur récupération modèles:', error.message);
            return [];
        }
    }

    /**
     * Pré-charge un modèle pour éviter les timeouts
     */
    async preloadModel(modelName) {
        try {
            console.log(`🔄 Pré-chargement du modèle: ${modelName}`);

            // Faire un appel simple pour charger le modèle en mémoire
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                model: modelName,
                prompt: "Hello",
                stream: false,
                options: {
                    max_tokens: 1,
                    temperature: 0.1
                }
            }, {
                timeout: 300000 // 5 minutes pour le chargement initial
            });

            console.log(`✅ Modèle ${modelName} pré-chargé avec succès`);
            return true;
        } catch (error) {
            console.warn(`⚠️ Échec pré-chargement ${modelName}:`, error.message);
            return false;
        }
    }

    /**
     * Génère une réponse avec un modèle (avec gestion intelligente des timeouts)
     */
    async generateResponse(modelName, prompt, options = {}) {
        try {
            console.log(`🤖 Appel agent intégré: ${modelName}`);

            // Pour les gros modèles, essayer de pré-charger d'abord
            if (modelName.includes('34b') || modelName.includes('19gb') || modelName.includes('codellama')) {
                console.log(`🚀 Modèle lourd détecté (${modelName}), vérification du chargement...`);

                // Vérifier si le modèle est déjà chargé avec un ping rapide
                try {
                    await axios.post(`${this.baseUrl}/api/generate`, {
                        model: modelName,
                        prompt: "test",
                        stream: false,
                        options: { max_tokens: 1 }
                    }, { timeout: 10000 }); // 10 secondes pour vérifier
                    console.log(`✅ Modèle ${modelName} déjà chargé`);
                } catch (pingError) {
                    console.log(`🔄 Modèle non chargé, pré-chargement en cours...`);
                    await this.preloadModel(modelName);
                }
            }

            // Générer la réponse principale
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                model: modelName,
                prompt: prompt,
                stream: false,
                options: {
                    temperature: 0.8,
                    top_p: 0.9,
                    max_tokens: 2000,
                    num_thread: 4,
                    ...options
                }
            }, {
                timeout: 300000 // 5 minutes pour l'agent 19GB
            });

            if (response.data && response.data.response) {
                console.log(`✅ Réponse générée: ${response.data.response.length} caractères`);
                return response.data.response;
            } else {
                console.warn('⚠️ Réponse vide du modèle');
                return null;
            }
        } catch (error) {
            console.error(`❌ Erreur génération ${modelName}:`, error.message);

            // Si c'est un timeout et le prompt est long, essayer avec un prompt plus court
            if (error.message.includes('timeout') && prompt.length > 500) {
                console.log(`🔄 Retry avec prompt raccourci pour ${modelName}...`);
                const shortPrompt = prompt.substring(0, 300) + "\n\nRéponds brièvement.";
                return await this.generateResponseSimple(modelName, shortPrompt, options);
            }

            return null;
        }
    }

    /**
     * Version simplifiée pour les retry
     */
    async generateResponseSimple(modelName, prompt, options = {}) {
        try {
            console.log(`🔄 Génération simplifiée pour ${modelName}`);
            const response = await axios.post(`${this.baseUrl}/api/generate`, {
                model: modelName,
                prompt: prompt,
                stream: false,
                options: {
                    temperature: 0.7,
                    max_tokens: Math.min(options.max_tokens || 400, 400),
                    num_thread: 2
                }
            }, {
                timeout: 120000 // 2 minutes pour le retry
            });

            if (response.data && response.data.response) {
                console.log(`✅ Réponse retry générée: ${response.data.response.length} caractères`);
                return response.data.response;
            }
            return null;
        } catch (error) {
            console.error(`❌ Erreur retry ${modelName}:`, error.message);
            return null;
        }
    }

    /**
     * Arrête Ollama
     */
    async stopOllama() {
        try {
            if (this.ollamaProcess) {
                console.log('🛑 Arrêt d\'Ollama...');
                this.ollamaProcess.kill('SIGTERM');
                
                // Attendre l'arrêt
                await this.sleep(2000);
                
                if (this.ollamaProcess && !this.ollamaProcess.killed) {
                    this.ollamaProcess.kill('SIGKILL');
                }
                
                this.isRunning = false;
                this.ollamaProcess = null;
                console.log('✅ Ollama arrêté');
            }
        } catch (error) {
            console.error('❌ Erreur arrêt Ollama:', error.message);
        }
    }

    /**
     * Utilitaires
     */

    execPromise(command) {
        return new Promise((resolve, reject) => {
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve({ stdout, stderr });
                }
            });
        });
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Obtient le statut d'Ollama
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            port: this.port,
            baseUrl: this.baseUrl,
            ollamaPath: this.ollamaPath,
            modelsPath: this.modelsPath
        };
    }
}

// Instance globale
const ollamaIntegration = new OllamaDirectIntegration();

module.exports = {
    OllamaDirectIntegration,
    ollamaIntegration
};
