/**
 * Système de mémoire conversationnelle pour Louna AI
 * Gestion avancée de la mémoire des conversations
 */

const { getLogger } = require('./utils/logger');
const EventEmitter = require('events');

class ConversationMemorySystem extends EventEmitter {
  constructor() {
    super();
    this.logger = getLogger();
    
    this.state = {
      isActive: false,
      totalConversations: 0,
      activeConversations: new Map(),
      conversationHistory: new Map(),
      memoryCapacity: 10000,
      compressionThreshold: 1000
    };
    
    this.config = {
      enablePersistence: true,
      enableCompression: true,
      enableContextualMemory: true,
      maxConversationLength: 500,
      memoryRetentionDays: 30,
      compressionRatio: 0.7,
      contextWindow: 10
    };
    
    this.memoryTypes = {
      shortTerm: new Map(),
      longTerm: new Map(),
      contextual: new Map(),
      emotional: new Map(),
      factual: new Map()
    };
    
    this.initialize();
  }

  /**
   * Initialise le système de mémoire conversationnelle
   */
  async initialize() {
    this.logger.info('Initialisation du système de mémoire conversationnelle', {
      component: 'CONVERSATION_MEMORY',
      config: this.config,
      memoryCapacity: this.state.memoryCapacity
    });

    try {
      // Charger les conversations existantes
      await this.loadExistingConversations();
      
      // Initialiser les types de mémoire
      await this.initializeMemoryTypes();
      
      // Démarrer la compression automatique
      if (this.config.enableCompression) {
        this.startAutoCompression();
      }
      
      // Démarrer la persistance
      if (this.config.enablePersistence) {
        this.startPersistence();
      }
      
      this.state.isActive = true;
      
      this.logger.info('Système de mémoire conversationnelle initialisé', {
        component: 'CONVERSATION_MEMORY',
        status: 'active',
        totalConversations: this.state.totalConversations,
        memoryTypes: Object.keys(this.memoryTypes).length
      });
      
    } catch (error) {
      this.logger.error('Erreur lors de l\'initialisation du système de mémoire conversationnelle', {
        component: 'CONVERSATION_MEMORY',
        error: error.message
      });
    }
  }

  /**
   * Charge les conversations existantes
   */
  async loadExistingConversations() {
    try {
      // Simuler le chargement des conversations
      this.logger.info('Chargement des conversations existantes', {
        component: 'CONVERSATION_MEMORY'
      });
      
      // Ici on pourrait charger depuis un fichier ou une base de données
      // Pour l'instant, on simule avec des données vides
      
    } catch (error) {
      this.logger.warn('Erreur chargement conversations', {
        component: 'CONVERSATION_MEMORY',
        error: error.message
      });
    }
  }

  /**
   * Initialise les types de mémoire
   */
  async initializeMemoryTypes() {
    for (const [type, memory] of Object.entries(this.memoryTypes)) {
      try {
        // Initialiser chaque type de mémoire
        this.logger.info('Type de mémoire initialisé', {
          component: 'CONVERSATION_MEMORY',
          type,
          capacity: memory.size || 0
        });
      } catch (error) {
        this.logger.warn('Erreur initialisation type mémoire', {
          component: 'CONVERSATION_MEMORY',
          type,
          error: error.message
        });
      }
    }
  }

  /**
   * Démarre la compression automatique
   */
  startAutoCompression() {
    setInterval(() => {
      this.performCompression();
    }, 300000); // Toutes les 5 minutes
    
    this.logger.info('Compression automatique démarrée', {
      component: 'CONVERSATION_MEMORY',
      interval: 300000
    });
  }

  /**
   * Effectue la compression des conversations
   */
  async performCompression() {
    try {
      let compressedCount = 0;
      
      for (const [conversationId, conversation] of this.state.conversationHistory) {
        if (conversation.messages && conversation.messages.length > this.config.compressionThreshold) {
          await this.compressConversation(conversationId, conversation);
          compressedCount++;
        }
      }
      
      if (compressedCount > 0) {
        this.logger.info('Compression des conversations terminée', {
          component: 'CONVERSATION_MEMORY',
          compressedCount
        });
      }
      
    } catch (error) {
      this.logger.error('Erreur lors de la compression', {
        component: 'CONVERSATION_MEMORY',
        error: error.message
      });
    }
  }

  /**
   * Compresse une conversation
   */
  async compressConversation(conversationId, conversation) {
    try {
      const originalLength = conversation.messages.length;
      
      // Garder les messages les plus récents et les plus importants
      const recentMessages = conversation.messages.slice(-this.config.contextWindow);
      const importantMessages = conversation.messages.filter(msg => msg.importance > 0.8);
      
      // Combiner et dédupliquer
      const compressedMessages = [...new Set([...importantMessages, ...recentMessages])];
      
      conversation.messages = compressedMessages;
      conversation.compressed = true;
      conversation.originalLength = originalLength;
      conversation.compressionRatio = compressedMessages.length / originalLength;
      
      this.logger.info('Conversation compressée', {
        component: 'CONVERSATION_MEMORY',
        conversationId,
        originalLength,
        compressedLength: compressedMessages.length,
        ratio: conversation.compressionRatio.toFixed(2)
      });
      
    } catch (error) {
      this.logger.error('Erreur compression conversation', {
        component: 'CONVERSATION_MEMORY',
        conversationId,
        error: error.message
      });
    }
  }

  /**
   * Démarre la persistance
   */
  startPersistence() {
    setInterval(() => {
      this.saveConversations();
    }, 60000); // Toutes les minutes
    
    this.logger.info('Persistance automatique démarrée', {
      component: 'CONVERSATION_MEMORY',
      interval: 60000
    });
  }

  /**
   * Sauvegarde les conversations
   */
  async saveConversations() {
    try {
      // Simuler la sauvegarde
      this.logger.info('Conversations sauvegardées', {
        component: 'CONVERSATION_MEMORY',
        totalConversations: this.state.conversationHistory.size,
        activeConversations: this.state.activeConversations.size
      });
      
    } catch (error) {
      this.logger.error('Erreur sauvegarde conversations', {
        component: 'CONVERSATION_MEMORY',
        error: error.message
      });
    }
  }

  /**
   * Crée une nouvelle conversation
   */
  createConversation(userId, metadata = {}) {
    const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const conversation = {
      id: conversationId,
      userId,
      createdAt: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      messages: [],
      metadata: {
        ...metadata,
        messageCount: 0,
        totalTokens: 0,
        averageResponseTime: 0
      },
      context: {
        topics: new Set(),
        entities: new Set(),
        sentiment: 'neutral',
        complexity: 0
      }
    };
    
    this.state.activeConversations.set(conversationId, conversation);
    this.state.totalConversations++;
    
    this.logger.info('Nouvelle conversation créée', {
      component: 'CONVERSATION_MEMORY',
      conversationId,
      userId,
      totalConversations: this.state.totalConversations
    });
    
    return conversationId;
  }

  /**
   * Ajoute un message à une conversation
   */
  addMessage(conversationId, message) {
    const conversation = this.state.activeConversations.get(conversationId) ||
                        this.state.conversationHistory.get(conversationId);
    
    if (!conversation) {
      this.logger.warn('Conversation non trouvée', {
        component: 'CONVERSATION_MEMORY',
        conversationId
      });
      return false;
    }
    
    const messageData = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      content: message.content,
      role: message.role || 'user',
      tokens: message.tokens || this.estimateTokens(message.content),
      importance: message.importance || this.calculateImportance(message.content),
      metadata: message.metadata || {}
    };
    
    conversation.messages.push(messageData);
    conversation.metadata.messageCount++;
    conversation.metadata.totalTokens += messageData.tokens;
    conversation.lastActivity = new Date().toISOString();
    
    // Mettre à jour le contexte
    this.updateConversationContext(conversation, messageData);
    
    // Stocker dans la mémoire appropriée
    this.storeInMemory(messageData, conversation);
    
    this.logger.info('Message ajouté à la conversation', {
      component: 'CONVERSATION_MEMORY',
      conversationId,
      messageId: messageData.id,
      role: messageData.role,
      tokens: messageData.tokens
    });
    
    return messageData.id;
  }

  /**
   * Estime le nombre de tokens
   */
  estimateTokens(content) {
    // Estimation simple : ~4 caractères par token
    return Math.ceil(content.length / 4);
  }

  /**
   * Calcule l'importance d'un message
   */
  calculateImportance(content) {
    let importance = 0.5; // Base
    
    // Facteurs d'importance
    if (content.includes('?')) importance += 0.1; // Questions
    if (content.length > 100) importance += 0.1; // Messages longs
    if (/[A-Z]{2,}/.test(content)) importance += 0.1; // Emphase
    if (content.includes('important') || content.includes('urgent')) importance += 0.2;
    
    return Math.min(importance, 1.0);
  }

  /**
   * Met à jour le contexte de la conversation
   */
  updateConversationContext(conversation, message) {
    try {
      // Extraire les sujets (mots-clés simples)
      const words = message.content.toLowerCase().split(/\s+/);
      const keywords = words.filter(word => word.length > 4);
      keywords.forEach(keyword => conversation.context.topics.add(keyword));
      
      // Analyser le sentiment (simple)
      const positiveWords = ['bon', 'bien', 'excellent', 'parfait', 'super'];
      const negativeWords = ['mauvais', 'mal', 'terrible', 'horrible', 'nul'];
      
      const hasPositive = positiveWords.some(word => message.content.toLowerCase().includes(word));
      const hasNegative = negativeWords.some(word => message.content.toLowerCase().includes(word));
      
      if (hasPositive && !hasNegative) {
        conversation.context.sentiment = 'positive';
      } else if (hasNegative && !hasPositive) {
        conversation.context.sentiment = 'negative';
      } else {
        conversation.context.sentiment = 'neutral';
      }
      
      // Calculer la complexité
      conversation.context.complexity = Math.min(
        (message.content.length / 100) + (keywords.length / 10),
        1.0
      );
      
    } catch (error) {
      this.logger.warn('Erreur mise à jour contexte', {
        component: 'CONVERSATION_MEMORY',
        error: error.message
      });
    }
  }

  /**
   * Stocke un message dans la mémoire appropriée
   */
  storeInMemory(message, conversation) {
    try {
      // Mémoire à court terme (tous les messages récents)
      this.memoryTypes.shortTerm.set(message.id, {
        ...message,
        conversationId: conversation.id,
        expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24h
      });
      
      // Mémoire à long terme (messages importants)
      if (message.importance > 0.7) {
        this.memoryTypes.longTerm.set(message.id, {
          ...message,
          conversationId: conversation.id,
          storedAt: new Date().toISOString()
        });
      }
      
      // Mémoire contextuelle
      if (conversation.context.topics.size > 0) {
        this.memoryTypes.contextual.set(message.id, {
          ...message,
          conversationId: conversation.id,
          topics: Array.from(conversation.context.topics),
          sentiment: conversation.context.sentiment
        });
      }
      
      // Mémoire émotionnelle
      if (conversation.context.sentiment !== 'neutral') {
        this.memoryTypes.emotional.set(message.id, {
          ...message,
          conversationId: conversation.id,
          sentiment: conversation.context.sentiment,
          emotionalWeight: message.importance
        });
      }
      
      // Mémoire factuelle (pour les informations)
      if (message.role === 'assistant' && message.importance > 0.6) {
        this.memoryTypes.factual.set(message.id, {
          ...message,
          conversationId: conversation.id,
          factType: 'response',
          reliability: 0.8
        });
      }
      
    } catch (error) {
      this.logger.error('Erreur stockage mémoire', {
        component: 'CONVERSATION_MEMORY',
        messageId: message.id,
        error: error.message
      });
    }
  }

  /**
   * Récupère l'historique d'une conversation
   */
  getConversationHistory(conversationId, limit = 50) {
    const conversation = this.state.activeConversations.get(conversationId) ||
                        this.state.conversationHistory.get(conversationId);
    
    if (!conversation) {
      return null;
    }
    
    return {
      ...conversation,
      messages: conversation.messages.slice(-limit)
    };
  }

  /**
   * Recherche dans les conversations
   */
  searchConversations(query, options = {}) {
    const results = [];
    const searchTerm = query.toLowerCase();
    
    try {
      // Rechercher dans les conversations actives
      for (const [id, conversation] of this.state.activeConversations) {
        const matches = conversation.messages.filter(msg =>
          msg.content.toLowerCase().includes(searchTerm)
        );
        
        if (matches.length > 0) {
          results.push({
            conversationId: id,
            matches: matches.slice(0, options.maxMatches || 5),
            totalMatches: matches.length
          });
        }
      }
      
      // Rechercher dans l'historique si demandé
      if (options.includeHistory) {
        for (const [id, conversation] of this.state.conversationHistory) {
          const matches = conversation.messages.filter(msg =>
            msg.content.toLowerCase().includes(searchTerm)
          );
          
          if (matches.length > 0) {
            results.push({
              conversationId: id,
              matches: matches.slice(0, options.maxMatches || 5),
              totalMatches: matches.length
            });
          }
        }
      }
      
    } catch (error) {
      this.logger.error('Erreur recherche conversations', {
        component: 'CONVERSATION_MEMORY',
        query,
        error: error.message
      });
    }
    
    return results.slice(0, options.maxResults || 10);
  }

  /**
   * Archive une conversation
   */
  archiveConversation(conversationId) {
    const conversation = this.state.activeConversations.get(conversationId);
    
    if (!conversation) {
      return false;
    }
    
    // Déplacer vers l'historique
    this.state.conversationHistory.set(conversationId, {
      ...conversation,
      archivedAt: new Date().toISOString()
    });
    
    // Supprimer des conversations actives
    this.state.activeConversations.delete(conversationId);
    
    this.logger.info('Conversation archivée', {
      component: 'CONVERSATION_MEMORY',
      conversationId,
      messageCount: conversation.messages.length
    });
    
    return true;
  }

  /**
   * Nettoie les anciennes conversations
   */
  cleanupOldConversations() {
    const cutoffDate = Date.now() - (this.config.memoryRetentionDays * 24 * 60 * 60 * 1000);
    let cleanedCount = 0;
    
    try {
      // Nettoyer l'historique
      for (const [id, conversation] of this.state.conversationHistory) {
        const conversationDate = new Date(conversation.createdAt).getTime();
        if (conversationDate < cutoffDate) {
          this.state.conversationHistory.delete(id);
          cleanedCount++;
        }
      }
      
      // Nettoyer la mémoire à court terme
      for (const [id, memory] of this.memoryTypes.shortTerm) {
        if (memory.expiresAt < Date.now()) {
          this.memoryTypes.shortTerm.delete(id);
        }
      }
      
      if (cleanedCount > 0) {
        this.logger.info('Nettoyage des anciennes conversations', {
          component: 'CONVERSATION_MEMORY',
          cleanedCount,
          retentionDays: this.config.memoryRetentionDays
        });
      }
      
    } catch (error) {
      this.logger.error('Erreur nettoyage conversations', {
        component: 'CONVERSATION_MEMORY',
        error: error.message
      });
    }
  }

  /**
   * Obtient les statistiques du système
   */
  getStats() {
    const memoryStats = {};
    for (const [type, memory] of Object.entries(this.memoryTypes)) {
      memoryStats[type] = memory.size;
    }
    
    return {
      ...this.state,
      memoryStats,
      config: this.config,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Configure le système
   */
  configure(newConfig) {
    Object.assign(this.config, newConfig);
    
    this.logger.info('Configuration du système de mémoire conversationnelle mise à jour', {
      component: 'CONVERSATION_MEMORY',
      newConfig
    });
  }

  /**
   * Démarre le système
   */
  async start() {
    if (this.state.isActive) {
      this.logger.warn('Système de mémoire conversationnelle déjà démarré', {
        component: 'CONVERSATION_MEMORY'
      });
      return true;
    }

    await this.initialize();
    return true;
  }

  /**
   * Arrête le système
   */
  async stop() {
    this.state.isActive = false;
    
    // Sauvegarder avant l'arrêt
    await this.saveConversations();
    
    this.logger.info('Système de mémoire conversationnelle arrêté', {
      component: 'CONVERSATION_MEMORY'
    });
    
    return true;
  }
}

module.exports = ConversationMemorySystem;
